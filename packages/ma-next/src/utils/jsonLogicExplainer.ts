/**
 * Utility to convert JSON Logic conditions to human-readable explanations
 */

interface JsonLogicCondition {
  [operator: string]: any;
}

/**
 * Converts a JSON Logic condition to a human-readable explanation
 */
export function explainJsonLogic(condition: JsonLogicCondition): string {
  if (!condition || typeof condition !== 'object') {
    return 'No filter applied';
  }

  return explainCondition(condition);
}

function explainCondition(condition: JsonLogicCondition): string {
  const operator = Object.keys(condition)[0];
  const operands = condition[operator];

  switch (operator) {
    case 'and':
      if (Array.isArray(operands)) {
        const explanations = operands.map(explainCondition);
        return explanations.join(' AND ');
      }
      break;

    case 'or':
      if (Array.isArray(operands)) {
        const explanations = operands.map(explainCondition);
        return explanations.join(' OR ');
      }
      break;

    case '==':
      if (Array.isArray(operands) && operands.length === 2) {
        const [left, right] = operands;
        const leftDesc = describeValue(left);
        const rightDesc = describeValue(right);
        return `${leftDesc} equals ${rightDesc}`;
      }
      break;

    case '!=':
      if (Array.isArray(operands) && operands.length === 2) {
        const [left, right] = operands;
        const leftDesc = describeValue(left);
        const rightDesc = describeValue(right);
        return `${leftDesc} does not equal ${rightDesc}`;
      }
      break;

    case '>':
      if (Array.isArray(operands) && operands.length === 2) {
        const [left, right] = operands;
        const leftDesc = describeValue(left);
        const rightDesc = describeValue(right);
        return `${leftDesc} is greater than ${rightDesc}`;
      }
      break;

    case '<':
      if (Array.isArray(operands) && operands.length === 2) {
        const [left, right] = operands;
        const leftDesc = describeValue(left);
        const rightDesc = describeValue(right);
        return `${leftDesc} is less than ${rightDesc}`;
      }
      break;

    case '>=':
      if (Array.isArray(operands) && operands.length === 2) {
        const [left, right] = operands;
        const leftDesc = describeValue(left);
        const rightDesc = describeValue(right);
        return `${leftDesc} is greater than or equal to ${rightDesc}`;
      }
      break;

    case '<=':
      if (Array.isArray(operands) && operands.length === 2) {
        const [left, right] = operands;
        const leftDesc = describeValue(left);
        const rightDesc = describeValue(right);
        return `${leftDesc} is less than or equal to ${rightDesc}`;
      }
      break;

    case 'in':
      if (Array.isArray(operands) && operands.length === 2) {
        const [needle, haystack] = operands;
        const needleDesc = describeValue(needle);
        const haystackDesc = describeValue(haystack);
        return `${needleDesc} is in ${haystackDesc}`;
      }
      break;

    case '!':
      return `NOT (${explainCondition(operands)})`;

    default:
      return `${operator} condition applied`;
  }

  return 'Complex filter condition';
}

function describeValue(value: any): string {
  if (value && typeof value === 'object' && value.var) {
    // Handle {var: "field.name"} accessor
    const fieldPath = value.var;
    return humanizeFieldPath(fieldPath);
  }

  if (Array.isArray(value)) {
    if (value.length === 0) {
      return 'empty list';
    }
    return `[${value.map(describeValue).join(', ')}]`;
  }

  if (typeof value === 'string') {
    return `"${value}"`;
  }

  if (typeof value === 'number') {
    return value.toString();
  }

  if (typeof value === 'boolean') {
    return value ? 'true' : 'false';
  }

  if (value === null) {
    return 'null';
  }

  return JSON.stringify(value);
}

function humanizeFieldPath(path: string): string {
  // Convert technical field paths to more human-readable descriptions
  const humanizedPaths: Record<string, string> = {
    'labels': 'email labels',
    'subject': 'email subject',
    'sender': 'email sender',
    'body': 'email body',
    'channel_id': 'Slack channel',
    'message.files': 'message attachments',
    'message.files.length': 'number of attachments',
    'attachments': 'attachments',
    'attachments.length': 'number of attachments',
    'status': 'status',
    'isReceived': 'is received',
    'created_at': 'creation date',
    'updated_at': 'last updated',
  };

  return humanizedPaths[path] || path.replace(/\./g, ' → ');
}

/**
 * Gets a brief summary of the filter for display
 */
export function getFilterSummary(condition: JsonLogicCondition): string {
  if (!condition || typeof condition !== 'object') {
    return '';
  }

  const explanation = explainJsonLogic(condition);
  
  // Truncate long explanations
  if (explanation.length > 100) {
    return explanation.substring(0, 97) + '...';
  }
  
  return explanation;
}
