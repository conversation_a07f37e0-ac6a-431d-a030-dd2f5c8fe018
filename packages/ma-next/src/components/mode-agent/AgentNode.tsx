import clsx from 'clsx';
import { NodeStatus } from '../../types';
import { StatusIndicator } from './StatusIndicator';
import { FilterTooltip } from './FilterTooltip';

interface AgentNodeProps {
  icon: React.ReactNode;
  label: string;
  status: NodeStatus;
  testStatus: string;
  nodeIndex: number;
  condition?: any;
}

function AgentNode({ icon, label, status, testStatus, nodeIndex, condition }: AgentNodeProps) {
  const isProcessing = status === 'processing' || status === 'simulating';
  const isSuccess = status === 'success';
  const isActive = status === 'active';
  const isFailed = status === 'failed';

  return (
    <div className="flex flex-col items-center h-28 relative">
      <div
        className={clsx(
          'w-36 h-20 px-6 py-3 rounded-lg border-2 flex flex-col items-center justify-center group relative',
          {
            'border-indigo-400 dark:border-indigo-500 animate-pulse': isProcessing,
            'border-green-400 dark:border-green-500': isActive || isSuccess,
            'border-red-400 dark:border-red-500': isFailed,
            'border-gray-200 dark:border-gray-700':
              !isProcessing && !isActive && !isSuccess && !isFailed,
          }
        )}
      >
        {/* Filter icon in top-right corner */}
        {condition && (
          <div className="absolute top-1 right-1">
            <FilterTooltip condition={condition} />
          </div>
        )}

        {icon}
        <div className="mt-1 text-xs font-medium text-gray-700 dark:text-gray-300 text-nowrap">
          {label}
        </div>
      </div>

      {/* Status indicator */}
      <div className="h-6 mt-1 flex items-center justify-center">
        <StatusIndicator status={status} testStatus={testStatus} nodeIndex={nodeIndex} />
      </div>
    </div>
  );
}

export { AgentNode };
