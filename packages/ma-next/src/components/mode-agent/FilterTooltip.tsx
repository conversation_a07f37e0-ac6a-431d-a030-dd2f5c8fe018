import { useState } from 'react';
import { Filter } from 'lucide-react';
import { explainJsonLogic } from '../../utils/jsonLogicExplainer';

interface FilterTooltipProps {
  condition: any;
}

function FilterTooltip({ condition }: FilterTooltipProps) {
  const [isHovered, setIsHovered] = useState(false);

  if (!condition || typeof condition !== 'object') {
    return null;
  }

  const explanation = explainJsonLogic(condition);

  return (
    <div 
      className="relative"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Filter 
        className="w-3 h-3 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 cursor-help" 
      />
      
      {isHovered && (
        <div className="absolute top-full right-0 mt-1 z-50 w-80 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-3">
          <div className="flex items-center space-x-2 mb-2">
            <Filter className="w-4 h-4 text-gray-500" />
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              Filter Applied
            </span>
          </div>
          
          <div className="text-sm text-gray-700 dark:text-gray-300 mb-3">
            {explanation}
          </div>
          
          <details className="text-xs">
            <summary className="cursor-pointer text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
              View raw condition
            </summary>
            <pre className="mt-2 p-2 bg-gray-50 dark:bg-gray-900 rounded text-xs overflow-auto max-h-32 text-gray-600 dark:text-gray-400">
              {JSON.stringify(condition, null, 2)}
            </pre>
          </details>
        </div>
      )}
    </div>
  );
}

export { FilterTooltip };
