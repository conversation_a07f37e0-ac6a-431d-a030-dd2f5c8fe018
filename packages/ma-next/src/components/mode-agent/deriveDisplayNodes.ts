/**
 * Derives display nodes from taskflow schema nodes
 * @param taskflow The taskflow object with schema
 * @returns An array of display nodes for the agent visualization
 */
function deriveDisplayNodes(taskflow: any) {
  if (!taskflow?.schema) {
    return [];
  }

  // Get the trigger and action nodes
  const triggerNode = taskflow.schema.triggers?.[0];
  const actionNode = taskflow.schema.nodes?.[taskflow.schema.nodes.length - 1];

  // If we don't have both a trigger and an action node, return empty array
  if (!triggerNode || !actionNode) {
    return [];
  }

  // Determine provider and action keys for the last node
  let providerKey: string | undefined;
  let actionKey: string | undefined;

  if (actionNode.type.startsWith('provider.')) {
    const [, pKey, aKey] = actionNode.type.split('.');
    providerKey = pKey;
    actionKey = aKey;
  }

  // Create a simplified agent representation for the UI
  return [
    // Trigger node
    {
      id: triggerNode.id,
      type: 'trigger',
      providerKey: triggerNode.parameters?.providerKey,
      syncKey: triggerNode.parameters?.syncKey,
      model: triggerNode.parameters?.model,
      condition: triggerNode.condition,
    },
    // AI node
    {
      id: 'ai-1',
      type: 'ai',
      label: 'AI',
    },
    // Action node
    {
      id: actionNode.id,
      type: 'action',
      providerKey,
      actionKey,
    },
  ];
}

export { deriveDisplayNodes };
