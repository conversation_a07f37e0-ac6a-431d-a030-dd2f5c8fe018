import { FilterTooltip } from './FilterTooltip';

/**
 * Demo component to test FilterTooltip with various JSON Logic conditions
 * This can be temporarily added to a page to test the functionality
 */
function FilterTooltipDemo() {
  const testConditions = [
    {
      name: 'Gmail INBOX filter',
      condition: { in: ['INBOX', { var: 'labels' }] }
    },
    {
      name: 'Slack with attachments',
      condition: {
        and: [
          { '==': [{ var: 'channel_id' }, 'C08TJRTK3FE'] },
          { '>': [{ var: 'message.files.length' }, 0] }
        ]
      }
    },
    {
      name: 'Email with attachments',
      condition: {
        and: [
          { in: ['INBOX', { var: 'labels' }] },
          { '>': [{ var: 'attachments.length' }, 0] }
        ]
      }
    },
    {
      name: 'Status not draft',
      condition: { '!=': [{ var: 'status' }, 'draft'] }
    }
  ];

  return (
    <div className="p-8 space-y-6">
      <h2 className="text-xl font-bold">Filter Tooltip Demo</h2>
      <div className="grid grid-cols-2 gap-4">
        {testConditions.map(({ name, condition }) => (
          <div key={name} className="p-4 border rounded-lg">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">{name}</span>
              <FilterTooltip condition={condition} />
            </div>
            <pre className="mt-2 text-xs text-gray-500 overflow-auto">
              {JSON.stringify(condition, null, 2)}
            </pre>
          </div>
        ))}
      </div>
    </div>
  );
}

export { FilterTooltipDemo };
