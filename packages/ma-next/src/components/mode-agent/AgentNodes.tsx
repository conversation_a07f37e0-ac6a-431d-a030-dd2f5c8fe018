import React, { Dispatch, SetStateAction } from 'react';
import { getNodeStatus } from '../../utils/getNodeStatus';
import { ConnectionLine } from './ConnectionLine';
import { AINode } from './AINode';
import { AgentNode } from './AgentNode';
import { TestStatus } from '../../types';
import {
  ICON_LOOKUP,
  ProviderType,
  getActionLabel,
  getSyncLabel,
} from 'src/config/agentLabels';
import { GmailIcon } from 'components/icons/providers';

interface AgentNodesProps {
  nodes: any[];
  testStatus?: TestStatus;
  isActive: boolean;
  advancedOptions?: boolean;
  setAdvancedOptions?: Dispatch<SetStateAction<boolean>>;
}

function AgentNodes({
  nodes,
  testStatus = 'idle',
  isActive,
  advancedOptions = false,
  setAdvancedOptions,
}: AgentNodesProps) {
  return (
    <div className="flex flex-col sm:flex-row items-center justify-between flex-wrap">
      {nodes.map((node, index) => {
        const nodeStatus = getNodeStatus(index, testStatus, isActive && index === 0);

        // Get the provider from node config
        const provider = node.providerKey as ProviderType;

        return (
          <React.Fragment key={node.id}>
            {index > 0 && <ConnectionLine />}
            {node.type === 'ai' ? (
              <AINode
                status={nodeStatus}
                testStatus={testStatus}
                nodeIndex={index}
                advancedOptions={advancedOptions}
                setAdvancedOptions={setAdvancedOptions}
              />
            ) : (
              <AgentNode
                icon={provider && provider in ICON_LOOKUP ? ICON_LOOKUP[provider] : <GmailIcon />}
                label={
                  node.syncKey
                    ? getSyncLabel(provider, node.syncKey)
                    : node.actionKey
                    ? getActionLabel(provider, node.actionKey)
                    : node.syncKey || node.actionKey
                }
                status={nodeStatus}
                testStatus={testStatus}
                nodeIndex={index}
                condition={node.condition}
              />
            )}
          </React.Fragment>
        );
      })}
    </div>
  );
}

export { AgentNodes };
